@echo off
chcp 65001 >nul
title سجل كشف الحساب الدوري - Account Statement Application

echo.
echo ========================================
echo    سجل كشف الحساب الدوري
echo    Account Statement Application
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM تشغيل التطبيق
echo 🚀 تشغيل التطبيق...
python run_app.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo.
)

echo.
echo تم إنهاء التطبيق
pause
