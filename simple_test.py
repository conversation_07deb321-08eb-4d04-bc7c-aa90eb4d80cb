#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للواجهة
"""

import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def clear_form():
    """مسح النموذج"""
    try:
        # التحقق من وجود نص محدد
        focused_widget = root.focus_get()
        
        if focused_widget and hasattr(focused_widget, 'selection_present'):
            try:
                if focused_widget.selection_present():
                    # حذف النص المحدد فقط
                    focused_widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                    messagebox.showinfo("تم", "تم حذف النص المحدد!")
                    return
            except tk.TclError:
                pass
        
        # مسح جميع الحقول
        entry1.delete(0, tk.END)
        entry1.insert(0, datetime.now().strftime("%Y-%m-%d"))
        entry2.delete(0, tk.END)
        entry3.delete(0, tk.END)
        entry4.delete(0, tk.END)
        
        messagebox.showinfo("تم", "تم مسح جميع الحقول!")
        
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ: {e}")

def add_data():
    """إضافة البيانات"""
    data = f"التاريخ: {entry1.get()}\nالكود: {entry2.get()}\nالبيان: {entry3.get()}\nالمبلغ: {entry4.get()}"
    messagebox.showinfo("البيانات", data)

# إنشاء النافذة
root = tk.Tk()
root.title("اختبار زر المسح - سجل كشف الحساب")
root.geometry("700x500")
root.configure(bg='#f0f0f0')

# العنوان
title_frame = tk.Frame(root, bg='#2c3e50', height=60)
title_frame.pack(fill='x', pady=(0, 10))
title_frame.pack_propagate(False)

title_label = tk.Label(title_frame, text="اختبار زر المسح", 
                      font=('Arial Unicode MS', 16, 'bold'), fg='white', bg='#2c3e50')
title_label.pack(expand=True)

# إطار الإدخال
input_frame = tk.LabelFrame(root, text="اختبار الحقول", 
                           font=('Arial Unicode MS', 12, 'bold'), bg='#ecf0f1')
input_frame.pack(fill='x', padx=20, pady=10)

# الحقول
fields = [
    ("التاريخ:", "2024-12-19"),
    ("كود الحساب:", ""),
    ("البيان:", ""),
    ("المبلغ:", "")
]

entries = []

for i, (label_text, default_value) in enumerate(fields):
    row = tk.Frame(input_frame, bg='#ecf0f1')
    row.pack(fill='x', padx=10, pady=5)
    
    tk.Label(row, text=label_text, font=('Arial Unicode MS', 11), bg='#ecf0f1').pack(side='right', padx=5)
    entry = tk.Entry(row, font=('Arial Unicode MS', 11), width=25)
    entry.pack(side='right', padx=5)
    entry.insert(0, default_value)
    entries.append(entry)

entry1, entry2, entry3, entry4 = entries

# الأزرار
buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
buttons_frame.pack(fill='x', padx=10, pady=15)

add_btn = tk.Button(buttons_frame, text="إضافة", font=('Arial Unicode MS', 11, 'bold'),
                   bg='#27ae60', fg='white', width=10, command=add_data)
add_btn.pack(side='left', padx=5)

clear_btn = tk.Button(buttons_frame, text="مسح", font=('Arial Unicode MS', 11, 'bold'),
                     bg='#e74c3c', fg='white', width=10, command=clear_form)
clear_btn.pack(side='left', padx=5)

# التعليمات
instructions = tk.Label(root, text="""
تعليمات الاختبار:
1. أدخل نص في الحقول
2. حدد جزء من النص بالماوس
3. اضغط زر "مسح" - سيحذف النص المحدد فقط
4. إذا لم تحدد نص واضغطت "مسح" - سيمسح جميع الحقول
""", font=('Arial Unicode MS', 10), bg='#f0f0f0', fg='#2c3e50', justify='right')
instructions.pack(pady=20)

# رسالة الحالة
status = tk.Label(root, text="✅ البرنامج يعمل! جرب اختبار زر المسح", 
                 font=('Arial Unicode MS', 12, 'bold'), bg='#f0f0f0', fg='#27ae60')
status.pack(pady=10)

print("🚀 تم تشغيل اختبار زر المسح")
print("📋 يجب أن تظهر نافذة الاختبار الآن")

# تشغيل الواجهة
if __name__ == "__main__":
    try:
        root.mainloop()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل الواجهة: {e}")
