# سجل كشف الحساب الدوري

تطبيق محاسبي متقدم لإدارة كشف الحساب الدوري باللغة العربية مع واجهة مستخدم احترافية.

## المميزات

### 🔍 **البحث التلقائي للحسابات**
- عند كتابة كود الحساب، يظهر اسم الحساب تلقائياً
- دليل حسابات شامل يغطي جميع أنواع الحسابات المحاسبية
- التحقق من صحة كود الحساب قبل الإدخال

### 📊 **إدارة العمليات المحاسبية**
- إضافة العمليات المدينة والدائنة
- حساب الأرصدة تلقائياً
- عرض الإحصائيات الفورية (إجمالي المدين، الدائن، الرصيد النهائي)
- ترتيب العمليات حسب التاريخ

### 🔎 **البحث والفلترة**
- البحث بكود الحساب
- فلترة العمليات حسب الحساب المحدد
- عرض جميع العمليات أو العمليات المفلترة

### 💾 **حفظ وتصدير البيانات**
- حفظ البيانات في ملف JSON
- تحميل البيانات المحفوظة
- تصدير البيانات إلى ملف Excel
- حذف العمليات المحددة

### 🎨 **واجهة مستخدم احترافية**
- تصميم عصري وجذاب
- دعم كامل للغة العربية
- ألوان متناسقة ومريحة للعين
- تخطيط منظم وسهل الاستخدام

## متطلبات التشغيل

### المتطلبات الأساسية
```bash
Python 3.7+
tkinter (مدمج مع Python)
```

### المتطلبات الاختيارية (للتصدير إلى Excel)
```bash
pip install pandas openpyxl
```

## طريقة التشغيل

### 1. تشغيل التطبيق
```bash
python account_statement.py
```

### 2. استخدام التطبيق

#### إدخال عملية جديدة:
1. **التاريخ**: يتم ملؤه تلقائياً بالتاريخ الحالي
2. **رقم المستند**: أدخل رقم المستند المحاسبي
3. **كود الحساب**: أدخل كود الحساب (سيظهر اسم الحساب تلقائياً)
4. **البيان**: وصف العملية المحاسبية
5. **المبلغ**: قيمة العملية
6. **نوع العملية**: اختر مدين أو دائن
7. اضغط **إضافة** لحفظ العملية

#### البحث والفلترة:
- استخدم حقل "البحث بكود الحساب" للبحث عن عمليات حساب معين
- اضغط "عرض الكل" لإظهار جميع العمليات

#### إدارة البيانات:
- **حفظ البيانات**: حفظ جميع العمليات في ملف
- **تحميل البيانات**: استرداد العمليات المحفوظة
- **تصدير إلى Excel**: إنشاء ملف Excel بجميع العمليات
- **حذف العملية**: حذف العملية المحددة من الجدول

## دليل الحسابات المدمج

التطبيق يحتوي على دليل حسابات شامل يشمل:

### 1️⃣ الموجودات (Assets)
- **الموجودات الثابتة**: أراضي، مباني، آلات ومعدات، وسائل نقل، أثاث
- **مشروعات تحت التنفيذ**: جميع أنواع المشاريع قيد الإنجاز
- **المخزون**: خامات، وقود، أدوات احتياطية، منتجات تامة
- **القروض الممنوحة**: قروض طويلة وقصيرة الأجل
- **الاستثمارات المالية**: استثمارات طويلة وقصيرة الأجل
- **المدينون**: عملاء، أوراق قبض، حسابات مدينة
- **النقود**: صناديق، مصارف، شيكات

### 2️⃣ المطلوبات (Liabilities)
- **رأس المال**: رأس المال المدفوع
- **الاحتياطات**: احتياطات رأسمالية وعامة ومتنوعة
- **التخصيصات**: مخصصات الاندثار والديون المشكوك فيها
- **القروض المستلمة**: قروض طويلة وقصيرة الأجل
- **الدائنون**: مجهزون، أوراق دفع، حسابات دائنة

### 3️⃣ الاستخدامات (Expenses)
- **رواتب وأجور**: رواتب الموظفين والعمال والمساهمات الاجتماعية
- **المستلزمات السلعية**: خامات، وقود، أدوات، مواد تعبئة
- **المستلزمات الخدمية**: صيانة، دعاية، نقل، استئجار
- **مقاولات وخدمات**: مقاولات ثانوية وخدمات تشغيل
- **الاندثار**: اندثار جميع أنواع الموجودات الثابتة

### 4️⃣ الموارد (Revenues)
- **إيراد الإنتاج السلعي**: صناعات استخراجية وتحويلية
- **إيراد النشاط التجاري**: مبيعات وعمولات
- **إيراد النشاط الخدمي**: خدمات النقل والاتصالات والصيانة
- **الفوائد والإيجارات**: فوائد دائنة وإيجارات أراضي
- **الإعانات**: إعانات مختلفة للإنتاج والتصدير

## الملفات المنتجة

- **account_transactions.json**: ملف حفظ البيانات
- **account_statement_YYYYMMDD_HHMMSS.xlsx**: ملفات Excel المصدرة

## الدعم الفني

في حالة وجود أي مشاكل أو استفسارات، يرجى التأكد من:
1. تثبيت Python بشكل صحيح
2. وجود مكتبة tkinter (مدمجة عادة مع Python)
3. تثبيت pandas و openpyxl للتصدير إلى Excel

## الترخيص

هذا التطبيق مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تم تطوير هذا التطبيق لتسهيل العمل المحاسبي وتوفير أداة احترافية لإدارة كشف الحساب الدوري باللغة العربية.**
