#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سجل كشف الحساب الدوري - النسخة العاملة
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import json
import os

class AccountStatementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("سجل كشف الحساب الدوري")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # تحديد الخط العربي
        self.arabic_font = ('Arial Unicode MS', 10)
        self.arabic_font_bold = ('Arial Unicode MS', 10, 'bold')
        self.arabic_font_large = ('Arial Unicode MS', 12, 'bold')
        
        # قاموس الحسابات المبسط
        self.accounts_dict = {
            "1811": "صندوق المركز",
            "183": "نقدية لدى المصارف",
            "161": "عملاء",
            "261": "مجهزون",
            "321": "الخامات و المواد الاولية",
            "322": "الوقود والزيوت",
            "3111": "رواتب",
            "4121": "صافي المبيعات",
            "1161": "اثاث"
        }
        
        # قائمة العمليات
        self.transactions = []
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات المحفوظة
        self.load_data()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="سجل كشف الحساب الدوري", 
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.root, text="إدخال العملية", 
                                   font=self.arabic_font_bold, bg='#ecf0f1', 
                                   relief='groove', bd=2)
        input_frame.pack(fill='x', padx=10, pady=5)
        
        # الصف الأول - التاريخ ورقم المستند
        row1_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # التاريخ
        tk.Label(row1_frame, text="التاريخ:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.date_entry = tk.Entry(row1_frame, font=self.arabic_font, width=12)
        self.date_entry.pack(side='right', padx=5)
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # رقم المستند
        tk.Label(row1_frame, text="رقم المستند:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.doc_number_entry = tk.Entry(row1_frame, font=self.arabic_font, width=15)
        self.doc_number_entry.pack(side='right', padx=5)

        # الصف الثاني - كود الحساب واسم الحساب
        row2_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row2_frame.pack(fill='x', padx=10, pady=5)

        # كود الحساب
        tk.Label(row2_frame, text="كود الحساب:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.account_code_entry = tk.Entry(row2_frame, font=self.arabic_font, width=15, justify='left')
        self.account_code_entry.pack(side='right', padx=5)
        self.account_code_entry.bind('<KeyRelease>', self.on_account_code_change)
        # تحسين إدخال الأرقام فقط
        self.account_code_entry.bind('<Key>', self.validate_account_code_input)

        # اسم الحساب (للعرض فقط)
        tk.Label(row2_frame, text="اسم الحساب:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.account_name_var = tk.StringVar()
        self.account_name_label = tk.Label(row2_frame, textvariable=self.account_name_var,
                                          font=self.arabic_font, bg='#ecf0f1', fg='#27ae60', width=30)
        self.account_name_label.pack(side='right', padx=5)

        # الصف الثالث - البيان والمبلغ
        row3_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row3_frame.pack(fill='x', padx=10, pady=5)

        # البيان
        tk.Label(row3_frame, text="البيان:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.description_entry = tk.Entry(row3_frame, font=self.arabic_font, width=30)
        self.description_entry.pack(side='right', padx=5)

        # المبلغ
        tk.Label(row3_frame, text="المبلغ:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.amount_entry = tk.Entry(row3_frame, font=self.arabic_font, width=15)
        self.amount_entry.pack(side='right', padx=5)

        # الصف الرابع - نوع العملية والأزرار
        row4_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row4_frame.pack(fill='x', padx=10, pady=5)

        # نوع العملية
        tk.Label(row4_frame, text="نوع العملية:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.transaction_type = tk.StringVar(value="مدين")
        debit_radio = tk.Radiobutton(row4_frame, text="مدين", variable=self.transaction_type, 
                                    value="مدين", font=self.arabic_font, bg='#ecf0f1')
        debit_radio.pack(side='right', padx=5)
        credit_radio = tk.Radiobutton(row4_frame, text="دائن", variable=self.transaction_type, 
                                     value="دائن", font=self.arabic_font, bg='#ecf0f1')
        credit_radio.pack(side='right', padx=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(row4_frame, bg='#ecf0f1')
        buttons_frame.pack(side='left', padx=10)

        add_btn = tk.Button(buttons_frame, text="إضافة", font=self.arabic_font_bold, 
                           bg='#27ae60', fg='white', width=8, command=self.add_transaction)
        add_btn.pack(side='left', padx=2)

        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.arabic_font_bold, 
                             bg='#e74c3c', fg='white', width=8, command=self.clear_form)
        clear_btn.pack(side='left', padx=2)

        # جدول العمليات
        display_frame = tk.LabelFrame(self.root, text="كشف الحساب", 
                                     font=self.arabic_font_bold, bg='#ecf0f1', 
                                     relief='groove', bd=2)
        display_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('التاريخ', 'رقم المستند', 'كود الحساب', 'اسم الحساب', 'البيان', 'مدين', 'دائن', 'الرصيد')
        self.tree = ttk.Treeview(display_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.tree.heading(col, text=col)
            if col in ['مدين', 'دائن', 'الرصيد']:
                self.tree.column(col, width=100, anchor='center')
            elif col == 'كود الحساب':
                self.tree.column(col, width=80, anchor='center')
            elif col == 'اسم الحساب':
                self.tree.column(col, width=200, anchor='e')
            else:
                self.tree.column(col, width=120, anchor='center')

        scrollbar = ttk.Scrollbar(display_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)

        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.root, text="الإحصائيات", 
                                   font=self.arabic_font_bold, bg='#ecf0f1', 
                                   relief='groove', bd=2)
        stats_frame.pack(fill='x', padx=10, pady=5)

        stats_inner_frame = tk.Frame(stats_frame, bg='#ecf0f1')
        stats_inner_frame.pack(fill='x', padx=10, pady=5)

        # إجمالي المدين
        tk.Label(stats_inner_frame, text="إجمالي المدين:", font=self.arabic_font_bold, bg='#ecf0f1').pack(side='right', padx=10)
        self.total_debit_var = tk.StringVar(value="0.00")
        tk.Label(stats_inner_frame, textvariable=self.total_debit_var, font=self.arabic_font, 
                bg='#ecf0f1', fg='#e74c3c').pack(side='right', padx=5)

        # إجمالي الدائن
        tk.Label(stats_inner_frame, text="إجمالي الدائن:", font=self.arabic_font_bold, bg='#ecf0f1').pack(side='right', padx=10)
        self.total_credit_var = tk.StringVar(value="0.00")
        tk.Label(stats_inner_frame, textvariable=self.total_credit_var, font=self.arabic_font, 
                bg='#ecf0f1', fg='#27ae60').pack(side='right', padx=5)

        # الرصيد النهائي
        tk.Label(stats_inner_frame, text="الرصيد النهائي:", font=self.arabic_font_bold, bg='#ecf0f1').pack(side='right', padx=10)
        self.final_balance_var = tk.StringVar(value="0.00")
        tk.Label(stats_inner_frame, textvariable=self.final_balance_var, font=self.arabic_font_bold, 
                bg='#ecf0f1', fg='#2c3e50').pack(side='right', padx=5)

        print("✅ تم إنشاء جميع عناصر الواجهة")

    def validate_account_code_input(self, event):
        """التحقق من صحة إدخال كود الحساب - أرقام فقط"""
        # السماح بالمفاتيح الخاصة
        if event.keysym in ['BackSpace', 'Delete', 'Left', 'Right', 'Tab', 'Return']:
            return

        # السماح بالأرقام فقط
        if not event.char.isdigit():
            return "break"  # منع إدخال غير الأرقام

    def on_account_code_change(self, event):
        """تحديث اسم الحساب عند تغيير كود الحساب"""
        account_code = self.account_code_entry.get().strip()
        if account_code in self.accounts_dict:
            self.account_name_var.set(self.accounts_dict[account_code])
        else:
            if account_code:  # إذا كان هناك كود مدخل
                self.account_name_var.set("حساب غير موجود")
            else:  # إذا كان الحقل فارغ
                self.account_name_var.set("")

    def clear_form(self):
        """مسح النموذج - حذف البيانات المحددة في أي حقل أو مسح جميع الحقول"""
        try:
            # قائمة جميع حقول الإدخال
            entry_fields = [
                self.date_entry,
                self.doc_number_entry,
                self.account_code_entry,
                self.description_entry,
                self.amount_entry
            ]

            # البحث عن أي نص محدد في جميع الحقول
            text_selected = False
            for field in entry_fields:
                try:
                    if field.selection_present():
                        # حذف النص المحدد من هذا الحقل
                        start = field.index(tk.SEL_FIRST)
                        end = field.index(tk.SEL_LAST)
                        field.delete(start, end)
                        text_selected = True
                        print(f"✅ تم حذف النص المحدد من حقل")

                        # تحديث اسم الحساب إذا تم تعديل كود الحساب
                        if field == self.account_code_entry:
                            self.on_account_code_change(None)

                except tk.TclError:
                    # لا يوجد نص محدد في هذا الحقل
                    continue

            # إذا تم حذف نص محدد، لا نحتاج لمسح جميع الحقول
            if text_selected:
                return

            # إذا لم يكن هناك نص محدد، امسح جميع الحقول بشكل أفقي
            print("🔄 مسح جميع الحقول...")

            # مسح جميع الحقول من اليمين إلى اليسار (أفقي)
            self.date_entry.delete(0, tk.END)
            self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

            self.doc_number_entry.delete(0, tk.END)
            self.account_code_entry.delete(0, tk.END)
            self.description_entry.delete(0, tk.END)
            self.amount_entry.delete(0, tk.END)

            # مسح اسم الحساب المعروض
            self.account_name_var.set("")

            # إعادة تعيين نوع العملية
            self.transaction_type.set("مدين")

            # وضع التركيز على أول حقل (التاريخ)
            self.date_entry.focus_set()

            print("✅ تم مسح جميع الحقول بشكل أفقي")

        except Exception as e:
            print(f"❌ خطأ في مسح النموذج: {e}")
            messagebox.showerror("خطأ", f"خطأ في مسح النموذج: {e}")

    def add_transaction(self):
        """إضافة عملية جديدة"""
        try:
            # التحقق من صحة البيانات
            date_str = self.date_entry.get().strip()
            doc_number = self.doc_number_entry.get().strip()
            account_code = self.account_code_entry.get().strip()
            description = self.description_entry.get().strip()
            amount_str = self.amount_entry.get().strip()
            transaction_type = self.transaction_type.get()

            if not all([date_str, doc_number, account_code, description, amount_str]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if account_code not in self.accounts_dict:
                messagebox.showerror("خطأ", "كود الحساب غير موجود في دليل الحسابات")
                return

            try:
                amount = float(amount_str)
                if amount <= 0:
                    messagebox.showerror("خطأ", "المبلغ يجب أن يكون أكبر من الصفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "المبلغ يجب أن يكون رقماً صحيحاً")
                return

            # إنشاء العملية
            transaction = {
                'date': date_str,
                'doc_number': doc_number,
                'account_code': account_code,
                'account_name': self.accounts_dict[account_code],
                'description': description,
                'amount': amount,
                'type': transaction_type,
                'timestamp': datetime.now().isoformat()
            }

            self.transactions.append(transaction)
            self.refresh_display()
            self.clear_form()
            messagebox.showinfo("نجح", "تم إضافة العملية بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة العملية: {str(e)}")

    def refresh_display(self):
        """تحديث عرض العمليات"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        # ترتيب العمليات حسب التاريخ
        sorted_transactions = sorted(self.transactions, key=lambda x: x['date'])

        # حساب الأرصدة
        balance = 0
        total_debit = 0
        total_credit = 0

        for transaction in sorted_transactions:
            if transaction['type'] == 'مدين':
                debit_amount = f"{transaction['amount']:,.2f}"
                credit_amount = ""
                balance += transaction['amount']
                total_debit += transaction['amount']
            else:
                debit_amount = ""
                credit_amount = f"{transaction['amount']:,.2f}"
                balance -= transaction['amount']
                total_credit += transaction['amount']

            # إدراج العملية في الجدول
            self.tree.insert('', 'end', values=(
                transaction['date'],
                transaction['doc_number'],
                transaction['account_code'],
                transaction['account_name'],
                transaction['description'],
                debit_amount,
                credit_amount,
                f"{balance:,.2f}"
            ))

        # تحديث الإحصائيات
        self.total_debit_var.set(f"{total_debit:,.2f}")
        self.total_credit_var.set(f"{total_credit:,.2f}")
        self.final_balance_var.set(f"{balance:,.2f}")

    def save_data(self):
        """حفظ البيانات في ملف JSON"""
        try:
            with open('account_transactions.json', 'w', encoding='utf-8') as f:
                json.dump(self.transactions, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("نجح", "تم حفظ البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def load_data(self):
        """تحميل البيانات من ملف JSON"""
        try:
            if os.path.exists('account_transactions.json'):
                # التحقق من حجم الملف
                if os.path.getsize('account_transactions.json') > 0:
                    with open('account_transactions.json', 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            self.transactions = json.loads(content)
                            self.refresh_display()
                            print("✅ تم تحميل البيانات")
                        else:
                            self.transactions = []
                            print("ℹ️ ملف البيانات فارغ")
                else:
                    self.transactions = []
                    print("ℹ️ ملف البيانات فارغ")
            else:
                self.transactions = []
                print("ℹ️ لا يوجد ملف بيانات محفوظ")
        except Exception as e:
            self.transactions = []
            print(f"⚠️ تم إنشاء قائمة عمليات جديدة بسبب خطأ في الملف: {e}")

def main():
    """تشغيل التطبيق"""
    print("🚀 بدء تشغيل التطبيق...")

    try:
        root = tk.Tk()
        print("✅ تم إنشاء النافذة الرئيسية")

        app = AccountStatementApp(root)
        print("✅ تم إنشاء التطبيق")

        print("🎯 تشغيل الحلقة الرئيسية...")
        root.mainloop()

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
