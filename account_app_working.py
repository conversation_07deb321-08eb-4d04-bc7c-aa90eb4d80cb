#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سجل كشف الحساب الدوري - النسخة العاملة
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import json
import os

class AccountStatementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("سجل كشف الحساب الدوري - نظام القيد المزدوج")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')
        self.root.state('zoomed')  # فتح النافذة بحجم كامل
        
        # تحديد الخطوط العربية المحسنة
        self.arabic_font = ('Tahoma', 11)
        self.arabic_font_bold = ('Tahoma', 11, 'bold')
        self.arabic_font_large = ('Tahoma', 14, 'bold')
        self.arabic_font_header = ('Tahoma', 12, 'bold')
        self.arabic_font_tree = ('Tahoma', 10)
        
        # قاموس الحسابات الشامل
        self.accounts_dict = self.load_complete_accounts()
        
        # قائمة العمليات
        self.transactions = []
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات المحفوظة
        self.load_data()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار العنوان المحسن
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=70)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        # إضافة تدرج بصري للعنوان
        title_inner_frame = tk.Frame(title_frame, bg='#34495e')
        title_inner_frame.pack(fill='both', expand=True, padx=3, pady=3)

        title_label = tk.Label(title_inner_frame, text="📊 سجل كشف الحساب الدوري - نظام القيد المزدوج",
                              font=('Tahoma', 16, 'bold'), fg='#ecf0f1', bg='#34495e')
        title_label.pack(expand=True)

        # خط فاصل أنيق
        separator = tk.Frame(self.root, height=2, bg='#bdc3c7')
        separator.pack(fill='x', padx=10)
        
        # إطار الإدخال المحسن
        input_frame = tk.LabelFrame(self.root, text="📝 إدخال القيد المزدوج",
                                   font=self.arabic_font_header, bg='#ecf0f1',
                                   relief='groove', bd=3, fg='#2c3e50')
        input_frame.pack(fill='x', padx=10, pady=8)
        
        # الصف الأول - التاريخ ورقم المستند
        row1_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # التاريخ
        tk.Label(row1_frame, text="التاريخ:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.date_entry = tk.Entry(row1_frame, font=self.arabic_font, width=12)
        self.date_entry.pack(side='right', padx=5)
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # نوع المستند
        tk.Label(row1_frame, text="نوع المستند:", font=self.arabic_font_bold, bg='#ecf0f1', fg='#2c3e50').pack(side='right', padx=5)
        self.doc_type_var = tk.StringVar()
        self.doc_type_combo = ttk.Combobox(row1_frame, textvariable=self.doc_type_var, font=self.arabic_font, width=12, state='readonly')
        self.doc_type_combo['values'] = ('قبض', 'صرف', 'قيد')
        self.doc_type_combo.pack(side='right', padx=5)
        self.doc_type_combo.set('قيد')  # القيمة الافتراضية

        # رقم المستند
        tk.Label(row1_frame, text="رقم المستند:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.doc_number_entry = tk.Entry(row1_frame, font=self.arabic_font, width=15)
        self.doc_number_entry.pack(side='right', padx=5)

        # الصف الثاني - الحساب المدين
        row2_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row2_frame.pack(fill='x', padx=10, pady=5)

        # كود الحساب المدين
        tk.Label(row2_frame, text="كود الحساب المدين:", font=self.arabic_font_bold, bg='#ecf0f1', fg='#e74c3c').pack(side='right', padx=5)
        self.debit_account_code_entry = tk.Entry(row2_frame, font=self.arabic_font, width=15, justify='left')
        self.debit_account_code_entry.pack(side='right', padx=5)
        self.debit_account_code_entry.bind('<KeyRelease>', self.on_debit_account_code_change)
        self.debit_account_code_entry.bind('<Key>', self.validate_account_code_input)

        # اسم الحساب المدين (للعرض فقط)
        tk.Label(row2_frame, text="اسم الحساب المدين:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.debit_account_name_var = tk.StringVar()
        self.debit_account_name_label = tk.Label(row2_frame, textvariable=self.debit_account_name_var,
                                               font=self.arabic_font, bg='#ecf0f1', fg='#e74c3c', width=25)
        self.debit_account_name_label.pack(side='right', padx=5)

        # الصف الثالث - الحساب الدائن
        row3_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row3_frame.pack(fill='x', padx=10, pady=5)

        # كود الحساب الدائن
        tk.Label(row3_frame, text="كود الحساب الدائن:", font=self.arabic_font_bold, bg='#ecf0f1', fg='#27ae60').pack(side='right', padx=5)
        self.credit_account_code_entry = tk.Entry(row3_frame, font=self.arabic_font, width=15, justify='left')
        self.credit_account_code_entry.pack(side='right', padx=5)
        self.credit_account_code_entry.bind('<KeyRelease>', self.on_credit_account_code_change)
        self.credit_account_code_entry.bind('<Key>', self.validate_account_code_input)

        # اسم الحساب الدائن (للعرض فقط)
        tk.Label(row3_frame, text="اسم الحساب الدائن:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.credit_account_name_var = tk.StringVar()
        self.credit_account_name_label = tk.Label(row3_frame, textvariable=self.credit_account_name_var,
                                                font=self.arabic_font, bg='#ecf0f1', fg='#27ae60', width=25)
        self.credit_account_name_label.pack(side='right', padx=5)

        # الصف الرابع - البيان والمبلغ
        row4_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row4_frame.pack(fill='x', padx=10, pady=5)

        # البيان
        tk.Label(row4_frame, text="البيان:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.description_entry = tk.Entry(row4_frame, font=self.arabic_font, width=30)
        self.description_entry.pack(side='right', padx=5)

        # المبلغ
        tk.Label(row4_frame, text="المبلغ:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.amount_entry = tk.Entry(row4_frame, font=self.arabic_font, width=15)
        self.amount_entry.pack(side='right', padx=5)

        # الصف الخامس - الأزرار
        row5_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row5_frame.pack(fill='x', padx=10, pady=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(row5_frame, bg='#ecf0f1')
        buttons_frame.pack(side='left', padx=10)

        # تحسين تصميم الأزرار
        add_btn = tk.Button(buttons_frame, text="✅ إضافة قيد", font=self.arabic_font_bold,
                           bg='#27ae60', fg='white', width=12, height=2,
                           relief='flat', bd=0, cursor='hand2',
                           command=self.add_transaction)
        add_btn.pack(side='left', padx=5)

        clear_btn = tk.Button(buttons_frame, text="🗑️ مسح كشف الحساب", font=self.arabic_font_bold,
                             bg='#e74c3c', fg='white', width=15, height=2,
                             relief='flat', bd=0, cursor='hand2',
                             command=self.clear_form)
        clear_btn.pack(side='left', padx=5)

        # زر مسح حقول الإدخال
        clear_inputs_btn = tk.Button(buttons_frame, text="🔄 مسح الحقول", font=self.arabic_font_bold,
                                   bg='#f39c12', fg='white', width=12, height=2,
                                   relief='flat', bd=0, cursor='hand2',
                                   command=self.clear_input_fields)
        clear_inputs_btn.pack(side='left', padx=5)

        # جدول العمليات المحسن
        display_frame = tk.LabelFrame(self.root, text="📊 كشف الحساب - القيود المزدوجة",
                                     font=self.arabic_font_header, bg='#ecf0f1',
                                     relief='groove', bd=3, fg='#2c3e50')
        display_frame.pack(fill='both', expand=True, padx=10, pady=8)

        columns = ('البيان', 'دائن', 'مدين', 'اسم الحساب', 'كود الحساب', 'نوع ورقم المستند', 'التاريخ')
        self.tree = ttk.Treeview(display_frame, columns=columns, show='headings', height=12)

        # تحسين تصميم الجدول
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Treeview",
                       font=self.arabic_font_tree,
                       rowheight=25,
                       fieldbackground='#ffffff')
        style.configure("Treeview.Heading",
                       font=self.arabic_font_header,
                       background='#34495e',
                       foreground='white',
                       relief='flat')
        style.map("Treeview.Heading",
                 background=[('active', '#2c3e50')])

        for col in columns:
            self.tree.heading(col, text=col)
            if col == 'البيان':
                self.tree.column(col, width=250, anchor='center')
            elif col in ['مدين', 'دائن']:
                self.tree.column(col, width=130, anchor='center')
            elif col == 'اسم الحساب':
                self.tree.column(col, width=280, anchor='center')
            elif col == 'كود الحساب':
                self.tree.column(col, width=120, anchor='center')
            elif col == 'نوع ورقم المستند':
                self.tree.column(col, width=180, anchor='center')
            elif col == 'التاريخ':
                self.tree.column(col, width=110, anchor='center')
            else:
                self.tree.column(col, width=120, anchor='center')

        scrollbar = ttk.Scrollbar(display_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)

        # إطار الإحصائيات المحسن
        stats_frame = tk.LabelFrame(self.root, text="📈 الإحصائيات والتوازن",
                                   font=self.arabic_font_header, bg='#ecf0f1',
                                   relief='groove', bd=3, fg='#2c3e50')
        stats_frame.pack(fill='x', padx=10, pady=8)

        stats_inner_frame = tk.Frame(stats_frame, bg='#ecf0f1')
        stats_inner_frame.pack(fill='x', padx=15, pady=8)

        # إجمالي المدين مع تحسين التصميم
        debit_frame = tk.Frame(stats_inner_frame, bg='#e8f5e8', relief='ridge', bd=2)
        debit_frame.pack(side='right', padx=8, pady=2)
        tk.Label(debit_frame, text="💰 إجمالي المدين:", font=self.arabic_font_bold, bg='#e8f5e8').pack(side='top', padx=8, pady=2)
        self.total_debit_var = tk.StringVar(value="0.00")
        tk.Label(debit_frame, textvariable=self.total_debit_var, font=('Tahoma', 12, 'bold'),
                bg='#e8f5e8', fg='#c0392b').pack(side='top', padx=8, pady=2)

        # إجمالي الدائن مع تحسين التصميم
        credit_frame = tk.Frame(stats_inner_frame, bg='#e8f8f5', relief='ridge', bd=2)
        credit_frame.pack(side='right', padx=8, pady=2)
        tk.Label(credit_frame, text="💳 إجمالي الدائن:", font=self.arabic_font_bold, bg='#e8f8f5').pack(side='top', padx=8, pady=2)
        self.total_credit_var = tk.StringVar(value="0.00")
        tk.Label(credit_frame, textvariable=self.total_credit_var, font=('Tahoma', 12, 'bold'),
                bg='#e8f8f5', fg='#27ae60').pack(side='top', padx=8, pady=2)

        # عدد القيود مع تحسين التصميم
        count_frame = tk.Frame(stats_inner_frame, bg='#f8f9fa', relief='ridge', bd=2)
        count_frame.pack(side='right', padx=8, pady=2)
        tk.Label(count_frame, text="📋 عدد القيود:", font=self.arabic_font_bold, bg='#f8f9fa').pack(side='top', padx=8, pady=2)
        self.final_balance_var = tk.StringVar(value="0")
        tk.Label(count_frame, textvariable=self.final_balance_var, font=('Tahoma', 12, 'bold'),
                bg='#f8f9fa', fg='#2c3e50').pack(side='top', padx=8, pady=2)

        print("✅ تم إنشاء جميع عناصر الواجهة")

    def load_complete_accounts(self):
        """تحميل دليل الحسابات الشامل"""
        try:
            from accounts_data import get_all_accounts
            accounts = get_all_accounts()
            print(f"✅ تم تحميل {len(accounts)} حساب من دليل الحسابات")
            return accounts
        except ImportError:
            print("⚠️ لم يتم العثور على ملف دليل الحسابات، استخدام الحسابات الأساسية")
            # حسابات أساسية في حالة عدم وجود الملف الخارجي
            return {
                "1": "الموجودات",
                "1811": "صندوق المركز",
                "183": "نقدية لدى المصارف",
                "161": "عملاء",
                "261": "مجهزون",
                "321": "الخامات و المواد الاولية",
                "322": "الوقود والزيوت",
                "3111": "رواتب",
                "4121": "صافي المبيعات",
                "1161": "اثاث"
            }

    def validate_account_code_input(self, event):
        """التحقق من صحة إدخال كود الحساب - أرقام فقط"""
        # السماح بالمفاتيح الخاصة
        if event.keysym in ['BackSpace', 'Delete', 'Left', 'Right', 'Tab', 'Return']:
            return

        # السماح بالأرقام فقط
        if not event.char.isdigit():
            return "break"  # منع إدخال غير الأرقام

    def on_debit_account_code_change(self, event):
        """تحديث اسم الحساب المدين عند تغيير كود الحساب"""
        account_code = self.debit_account_code_entry.get().strip()
        if account_code in self.accounts_dict:
            self.debit_account_name_var.set(self.accounts_dict[account_code])
        else:
            if account_code:  # إذا كان هناك كود مدخل
                self.debit_account_name_var.set("حساب غير موجود")
            else:  # إذا كان الحقل فارغ
                self.debit_account_name_var.set("")

    def on_credit_account_code_change(self, event):
        """تحديث اسم الحساب الدائن عند تغيير كود الحساب"""
        account_code = self.credit_account_code_entry.get().strip()
        if account_code in self.accounts_dict:
            self.credit_account_name_var.set(self.accounts_dict[account_code])
        else:
            if account_code:  # إذا كان هناك كود مدخل
                self.credit_account_name_var.set("حساب غير موجود")
            else:  # إذا كان الحقل فارغ
                self.credit_account_name_var.set("")

    def clear_form(self):
        """مسح كشف الحساب - حذف العمليات المحددة بشكل أفقي احترافي"""
        try:
            # التحقق من وجود عمليات في كشف الحساب
            if not self.transactions:
                print("ℹ️ لا توجد عمليات لحذفها")
                return

            # الحصول على العمليات المحددة في الجدول
            selected_items = self.tree.selection()

            if selected_items:
                print(f"🔄 بدء حذف {len(selected_items)} عملية محددة بشكل أفقي...")
                # حذف العمليات المحددة بشكل احترافي
                self.delete_selected_transactions_professional(selected_items)
                print(f"✅ تم حذف {len(selected_items)} عملية محددة بشكل أفقي احترافي")
            else:
                print("🔄 بدء مسح جميع العمليات بشكل أفقي...")
                # مسح جميع العمليات في كشف الحساب بشكل احترافي
                self.clear_all_transactions_professional()
                print("✅ تم مسح جميع العمليات من كشف الحساب بشكل أفقي احترافي")

        except Exception as e:
            print(f"❌ خطأ في مسح كشف الحساب: {e}")

    def delete_selected_transactions(self, selected_items):
        """حذف العمليات المحددة من كشف الحساب بشكل أفقي"""
        try:
            # جمع بيانات العمليات المحددة للحذف
            transactions_to_delete = []

            # حذف العمليات من الجدول بشكل أفقي (من اليمين إلى اليسار)
            for item in selected_items:
                item_values = self.tree.item(item)['values']
                if len(item_values) >= 3:  # التأكد من وجود البيانات الأساسية
                    # البحث عن العملية في القائمة باستخدام التاريخ ورقم المستند وكود الحساب
                    for transaction in self.transactions:
                        if (transaction['date'] == item_values[0] and
                            transaction['doc_number'] == item_values[1] and
                            transaction['account_code'] == item_values[2]):
                            transactions_to_delete.append(transaction)
                            break

                # حذف العنصر من الجدول مباشرة (أفقي)
                self.tree.delete(item)

            # حذف العمليات من القائمة
            for transaction in transactions_to_delete:
                if transaction in self.transactions:
                    self.transactions.remove(transaction)

            # تحديث العرض والإحصائيات
            self.refresh_display()

        except Exception as e:
            print(f"❌ خطأ في حذف العمليات المحددة: {e}")

    def clear_all_transactions(self):
        """مسح جميع العمليات من كشف الحساب بشكل أفقي"""
        try:
            print("🔄 بدء مسح جميع العمليات من كشف الحساب...")

            # مسح الجدول بشكل أفقي (صف بصف من اليمين إلى اليسار)
            all_items = self.tree.get_children()
            for item in all_items:
                self.tree.delete(item)

            # مسح قائمة العمليات
            self.transactions.clear()

            # إعادة تعيين الإحصائيات إلى الصفر
            self.total_debit_var.set("0.00")
            self.total_credit_var.set("0.00")
            self.final_balance_var.set("0.00")

            print("✅ تم مسح جميع العمليات من كشف الحساب بشكل أفقي")

        except Exception as e:
            print(f"❌ خطأ في مسح جميع العمليات: {e}")

    def delete_selected_transactions_professional(self, selected_items):
        """حذف العمليات المحددة بشكل أفقي احترافي"""
        try:
            if not selected_items:
                print("⚠️ لا توجد عمليات محددة للحذف")
                return

            print(f"📋 تحديد {len(selected_items)} عملية للحذف...")

            # جمع معرفات العمليات المحددة للحذف
            items_to_delete = []
            transactions_to_remove = []

            # المرور عبر العناصر المحددة وجمع بياناتها
            for item in selected_items:
                try:
                    item_values = self.tree.item(item)['values']
                    if len(item_values) >= 7:  # التأكد من وجود جميع الأعمدة
                        # البحث عن العملية المطابقة في قائمة العمليات
                        # نبحث بالتاريخ ونوع ورقم المستند فقط للصف الأول (المدين)
                        if item_values[0] and item_values[1]:  # إذا كان التاريخ ونوع المستند موجودين (الصف الأول)
                            for transaction in self.transactions:
                                doc_info = f"{transaction.get('doc_type', 'قيد')} - {transaction.get('doc_number', 'غير محدد')}"
                                if (str(transaction['date']) == str(item_values[0]) and
                                    str(doc_info) == str(item_values[1])):

                                    items_to_delete.append(item)
                                    transactions_to_remove.append(transaction)
                                    print(f"🎯 تم تحديد العملية: {transaction.get('doc_number', 'غير محدد')} - {transaction['description']}")

                                    # البحث عن الصف المقابل (الدائن) وإضافته للحذف
                                    all_items = self.tree.get_children()
                                    current_index = list(all_items).index(item)
                                    if current_index + 1 < len(all_items):
                                        next_item = all_items[current_index + 1]
                                        next_values = self.tree.item(next_item)['values']
                                        # إذا كان الصف التالي هو الصف المقابل (بدون تاريخ ونوع مستند)
                                        if not next_values[0] and not next_values[1]:
                                            items_to_delete.append(next_item)
                                    break
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة عنصر: {e}")
                    continue

            # حذف العناصر من الجدول بشكل أفقي (من اليمين إلى اليسار)
            print("🗑️ حذف العناصر من الجدول بشكل أفقي...")
            for item in items_to_delete:
                try:
                    self.tree.delete(item)
                    print("➡️ تم حذف صف من الجدول")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف عنصر من الجدول: {e}")

            # حذف العمليات من قائمة البيانات
            print("📊 تحديث قائمة البيانات...")
            for transaction in transactions_to_remove:
                try:
                    if transaction in self.transactions:
                        self.transactions.remove(transaction)
                        print(f"✅ تم حذف العملية: {transaction['doc_number']}")
                except Exception as e:
                    print(f"⚠️ خطأ في حذف عملية من القائمة: {e}")

            # تحديث العرض والإحصائيات بشكل احترافي
            print("🔄 تحديث العرض والإحصائيات...")
            self.refresh_display()

            print(f"🎉 تم حذف {len(transactions_to_remove)} عملية بنجاح بشكل أفقي احترافي")

        except Exception as e:
            print(f"❌ خطأ في الحذف الاحترافي: {e}")

    def clear_all_transactions_professional(self):
        """مسح جميع العمليات بشكل أفقي احترافي"""
        try:
            total_transactions = len(self.transactions)
            print(f"📊 إجمالي العمليات للمسح: {total_transactions}")

            if total_transactions == 0:
                print("ℹ️ لا توجد عمليات لمسحها")
                return

            # مسح الجدول بشكل أفقي احترافي (صف بصف)
            print("🗑️ مسح الجدول بشكل أفقي...")
            all_items = self.tree.get_children()

            for i, item in enumerate(all_items, 1):
                try:
                    self.tree.delete(item)
                    print(f"➡️ تم مسح الصف {i} من {len(all_items)}")
                except Exception as e:
                    print(f"⚠️ خطأ في مسح الصف {i}: {e}")

            # مسح قائمة العمليات
            print("📋 مسح قائمة العمليات...")
            self.transactions.clear()

            # إعادة تعيين الإحصائيات بشكل احترافي
            print("📊 إعادة تعيين الإحصائيات...")
            self.total_debit_var.set("0.00")
            self.total_credit_var.set("0.00")
            self.final_balance_var.set("0.00")

            print(f"🎉 تم مسح {total_transactions} عملية بشكل أفقي احترافي")

        except Exception as e:
            print(f"❌ خطأ في المسح الاحترافي: {e}")

    def clear_input_fields(self):
        """مسح حقول الإدخال فقط"""
        try:
            print("🔄 مسح حقول الإدخال...")

            # مسح جميع حقول الإدخال
            self.date_entry.delete(0, tk.END)
            self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

            self.doc_type_combo.set('قيد')  # إعادة تعيين نوع المستند
            self.doc_number_entry.delete(0, tk.END)
            self.debit_account_code_entry.delete(0, tk.END)
            self.credit_account_code_entry.delete(0, tk.END)
            self.description_entry.delete(0, tk.END)
            self.amount_entry.delete(0, tk.END)

            # مسح أسماء الحسابات المعروضة
            self.debit_account_name_var.set("")
            self.credit_account_name_var.set("")

            # وضع التركيز على حقل التاريخ
            self.date_entry.focus_set()

            print("✅ تم مسح حقول الإدخال")

        except Exception as e:
            print(f"❌ خطأ في مسح حقول الإدخال: {e}")
            messagebox.showerror("خطأ", f"خطأ في مسح حقول الإدخال: {e}")

    def add_transaction(self):
        """إضافة قيد مزدوج جديد"""
        try:
            # التحقق من صحة البيانات
            date_str = self.date_entry.get().strip()
            doc_type = self.doc_type_var.get().strip()
            doc_number = self.doc_number_entry.get().strip()
            debit_account_code = self.debit_account_code_entry.get().strip()
            credit_account_code = self.credit_account_code_entry.get().strip()
            description = self.description_entry.get().strip()
            amount_str = self.amount_entry.get().strip()

            if not all([date_str, doc_type, doc_number, debit_account_code, credit_account_code, description, amount_str]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if debit_account_code not in self.accounts_dict:
                messagebox.showerror("خطأ", "كود الحساب المدين غير موجود في دليل الحسابات")
                return

            if credit_account_code not in self.accounts_dict:
                messagebox.showerror("خطأ", "كود الحساب الدائن غير موجود في دليل الحسابات")
                return

            if debit_account_code == credit_account_code:
                messagebox.showerror("خطأ", "لا يمكن أن يكون الحساب المدين والدائن نفس الحساب")
                return

            try:
                amount = float(amount_str)
                if amount <= 0:
                    messagebox.showerror("خطأ", "المبلغ يجب أن يكون أكبر من الصفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "المبلغ يجب أن يكون رقماً صحيحاً")
                return

            # إنشاء القيد المزدوج
            double_entry = {
                'date': date_str,
                'doc_type': doc_type,
                'doc_number': doc_number,
                'debit_account_code': debit_account_code,
                'debit_account_name': self.accounts_dict[debit_account_code],
                'credit_account_code': credit_account_code,
                'credit_account_name': self.accounts_dict[credit_account_code],
                'description': description,
                'amount': amount,
                'timestamp': datetime.now().isoformat()
            }

            self.transactions.append(double_entry)
            self.refresh_display()
            self.clear_input_fields()
            print("✅ تم إضافة القيد المزدوج بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة القيد: {str(e)}")

    def refresh_display(self):
        """تحديث عرض القيود المزدوجة"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        # ترتيب العمليات حسب التاريخ
        sorted_transactions = sorted(self.transactions, key=lambda x: x['date'])

        # حساب الإحصائيات
        total_amount = 0

        for transaction in sorted_transactions:
            # تنسيق نوع ورقم المستند
            doc_info = f"{transaction.get('doc_type', 'قيد')} - {transaction.get('doc_number', 'غير محدد')}"

            # إدراج الطرف المدين
            # الترتيب الجديد: البيان، دائن، مدين، اسم الحساب، كود الحساب، نوع ورقم المستند، التاريخ
            self.tree.insert('', 'end', values=(
                transaction['description'],  # البيان في أقصى اليمين
                "",  # دائن فارغ
                f"{transaction['amount']:,.2f}",  # مدين
                transaction.get('debit_account_name', transaction.get('account_name', '')),
                transaction.get('debit_account_code', transaction.get('account_code', '')),
                doc_info,
                transaction['date']  # التاريخ في أقصى اليسار
            ))

            # إدراج الطرف الدائن
            self.tree.insert('', 'end', values=(
                "",  # البيان فارغ للصف الثاني
                f"{transaction['amount']:,.2f}",  # دائن
                "",  # مدين فارغ
                transaction.get('credit_account_name', ''),
                transaction.get('credit_account_code', ''),
                "",  # نوع ورقم المستند فارغ للصف الثاني
                ""  # التاريخ فارغ للصف الثاني
            ))

            total_amount += transaction['amount']

        # تحديث الإحصائيات
        self.total_debit_var.set(f"{total_amount:,.2f}")
        self.total_credit_var.set(f"{total_amount:,.2f}")
        self.final_balance_var.set(str(len(sorted_transactions)))  # عدد القيود

    def save_data(self):
        """حفظ البيانات في ملف JSON"""
        try:
            with open('account_transactions.json', 'w', encoding='utf-8') as f:
                json.dump(self.transactions, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("نجح", "تم حفظ البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def load_data(self):
        """تحميل البيانات من ملف JSON"""
        try:
            if os.path.exists('account_transactions.json'):
                # التحقق من حجم الملف
                if os.path.getsize('account_transactions.json') > 0:
                    with open('account_transactions.json', 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            self.transactions = json.loads(content)
                            self.refresh_display()
                            print("✅ تم تحميل البيانات")
                        else:
                            self.transactions = []
                            print("ℹ️ ملف البيانات فارغ")
                else:
                    self.transactions = []
                    print("ℹ️ ملف البيانات فارغ")
            else:
                self.transactions = []
                print("ℹ️ لا يوجد ملف بيانات محفوظ")
        except Exception as e:
            self.transactions = []
            print(f"⚠️ تم إنشاء قائمة عمليات جديدة بسبب خطأ في الملف: {e}")

def main():
    """تشغيل التطبيق"""
    print("🚀 بدء تشغيل التطبيق...")

    try:
        root = tk.Tk()
        print("✅ تم إنشاء النافذة الرئيسية")

        app = AccountStatementApp(root)
        print("✅ تم إنشاء التطبيق")

        print("🎯 تشغيل الحلقة الرئيسية...")
        root.mainloop()

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
