#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للواجهة الرسومية
Simple GUI test
"""

import tkinter as tk
from tkinter import messagebox

def test_clear():
    """اختبار وظيفة المسح"""
    entry1.delete(0, tk.END)
    entry2.delete(0, tk.END)
    entry3.delete(0, tk.END)
    messagebox.showinfo("نجح", "تم مسح الحقول!")

def test_add():
    """اختبار وظيفة الإضافة"""
    text = f"التاريخ: {entry1.get()}, الكود: {entry2.get()}, البيان: {entry3.get()}"
    messagebox.showinfo("البيانات", text)

# إنشاء النافذة الرئيسية
root = tk.Tk()
root.title("اختبار الواجهة - سجل كشف الحساب")
root.geometry("600x400")
root.configure(bg='#f0f0f0')

# إطار العنوان
title_frame = tk.Frame(root, bg='#2c3e50', height=60)
title_frame.pack(fill='x', pady=(0, 10))
title_frame.pack_propagate(False)

title_label = tk.Label(title_frame, text="اختبار سجل كشف الحساب الدوري", 
                      font=('Arial Unicode MS', 14, 'bold'), fg='white', bg='#2c3e50')
title_label.pack(expand=True)

# إطار الإدخال
input_frame = tk.LabelFrame(root, text="اختبار الحقول", 
                           font=('Arial Unicode MS', 10, 'bold'), bg='#ecf0f1')
input_frame.pack(fill='x', padx=20, pady=10)

# الصف الأول
row1 = tk.Frame(input_frame, bg='#ecf0f1')
row1.pack(fill='x', padx=10, pady=5)

tk.Label(row1, text="التاريخ:", font=('Arial Unicode MS', 10), bg='#ecf0f1').pack(side='right', padx=5)
entry1 = tk.Entry(row1, font=('Arial Unicode MS', 10), width=15)
entry1.pack(side='right', padx=5)
entry1.insert(0, "2024-12-19")

# الصف الثاني
row2 = tk.Frame(input_frame, bg='#ecf0f1')
row2.pack(fill='x', padx=10, pady=5)

tk.Label(row2, text="كود الحساب:", font=('Arial Unicode MS', 10), bg='#ecf0f1').pack(side='right', padx=5)
entry2 = tk.Entry(row2, font=('Arial Unicode MS', 10), width=15)
entry2.pack(side='right', padx=5)

# الصف الثالث
row3 = tk.Frame(input_frame, bg='#ecf0f1')
row3.pack(fill='x', padx=10, pady=5)

tk.Label(row3, text="البيان:", font=('Arial Unicode MS', 10), bg='#ecf0f1').pack(side='right', padx=5)
entry3 = tk.Entry(row3, font=('Arial Unicode MS', 10), width=30)
entry3.pack(side='right', padx=5)

# الأزرار
buttons_frame = tk.Frame(input_frame, bg='#ecf0f1')
buttons_frame.pack(fill='x', padx=10, pady=10)

add_btn = tk.Button(buttons_frame, text="اختبار إضافة", font=('Arial Unicode MS', 10, 'bold'),
                   bg='#27ae60', fg='white', width=12, command=test_add)
add_btn.pack(side='left', padx=5)

clear_btn = tk.Button(buttons_frame, text="اختبار مسح", font=('Arial Unicode MS', 10, 'bold'),
                     bg='#e74c3c', fg='white', width=12, command=test_clear)
clear_btn.pack(side='left', padx=5)

# رسالة تعليمات
instructions = tk.Label(root, text="اختبر الأزرار للتأكد من عمل الواجهة", 
                       font=('Arial Unicode MS', 12), bg='#f0f0f0', fg='#2c3e50')
instructions.pack(pady=20)

# رسالة حالة
status = tk.Label(root, text="✅ الواجهة تعمل بنجاح!", 
                 font=('Arial Unicode MS', 12, 'bold'), bg='#f0f0f0', fg='#27ae60')
status.pack(pady=10)

print("🚀 تم تشغيل واجهة الاختبار")
print("📋 يجب أن تظهر نافذة الاختبار الآن")

# تشغيل الواجهة
if __name__ == "__main__":
    try:
        root.mainloop()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة: {e}")
        messagebox.showerror("خطأ", f"خطأ في تشغيل الواجهة: {e}")
