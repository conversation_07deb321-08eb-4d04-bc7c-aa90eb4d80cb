#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إنشاء بيانات تجريبية لتطبيق سجل كشف الحساب الدوري
Sample data generator for Account Statement Application

هذا الملف ينشئ بيانات تجريبية لاختبار التطبيق
This file creates sample data for testing the application
"""

import json
from datetime import datetime, timedelta
import random

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    # عينة من الحسابات المستخدمة بكثرة
    common_accounts = [
        ("1811", "صندوق المركز"),
        ("183", "نقدية لدى المصارف"),
        ("161", "عملاء"),
        ("261", "مجهزون"),
        ("3111", "رواتب"),
        ("321", "الخامات و المواد الاولية"),
        ("4121", "صافي المبيعات"),
        ("322", "الوقود والزيوت"),
        ("1161", "اثاث"),
        ("2111", "رأس المال المدفوع")
    ]
    
    # أنواع البيانات التجريبية
    sample_descriptions = [
        "شراء مواد خام",
        "بيع منتجات",
        "دفع رواتب الموظفين",
        "تحصيل من العملاء",
        "دفع للمجهزين",
        "شراء أثاث مكتبي",
        "إيداع نقدي في البنك",
        "سحب نقدي من البنك",
        "شراء وقود للمعدات",
        "بيع منتجات نقداً"
    ]
    
    transactions = []
    base_date = datetime.now() - timedelta(days=30)
    
    # إنشاء 20 عملية تجريبية
    for i in range(20):
        # تاريخ عشوائي خلال الشهر الماضي
        random_days = random.randint(0, 30)
        transaction_date = base_date + timedelta(days=random_days)
        
        # اختيار حساب عشوائي
        account_code, account_name = random.choice(common_accounts)
        
        # اختيار بيان عشوائي
        description = random.choice(sample_descriptions)
        
        # مبلغ عشوائي
        amount = round(random.uniform(100, 10000), 2)
        
        # نوع العملية عشوائي
        transaction_type = random.choice(["مدين", "دائن"])
        
        transaction = {
            'date': transaction_date.strftime("%Y-%m-%d"),
            'doc_number': f"DOC-{1000 + i}",
            'account_code': account_code,
            'account_name': account_name,
            'description': description,
            'amount': amount,
            'type': transaction_type,
            'timestamp': transaction_date.isoformat()
        }
        
        transactions.append(transaction)
    
    # ترتيب العمليات حسب التاريخ
    transactions.sort(key=lambda x: x['date'])
    
    return transactions

def save_sample_data():
    """حفظ البيانات التجريبية"""
    try:
        sample_transactions = create_sample_data()
        
        with open('sample_account_transactions.json', 'w', encoding='utf-8') as f:
            json.dump(sample_transactions, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء ملف البيانات التجريبية: sample_account_transactions.json")
        print(f"📊 تم إنشاء {len(sample_transactions)} عملية تجريبية")
        
        # عرض ملخص البيانات
        total_debit = sum(t['amount'] for t in sample_transactions if t['type'] == 'مدين')
        total_credit = sum(t['amount'] for t in sample_transactions if t['type'] == 'دائن')
        
        print(f"\n📈 ملخص البيانات التجريبية:")
        print(f"   إجمالي المدين: {total_debit:,.2f}")
        print(f"   إجمالي الدائن: {total_credit:,.2f}")
        print(f"   الرصيد النهائي: {total_debit - total_credit:,.2f}")
        
        print(f"\n💡 لاستخدام البيانات التجريبية:")
        print(f"   1. انسخ محتوى ملف sample_account_transactions.json")
        print(f"   2. الصقه في ملف account_transactions.json")
        print(f"   3. أو استخدم زر 'تحميل البيانات' في التطبيق")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def copy_to_main_file():
    """نسخ البيانات التجريبية إلى الملف الرئيسي"""
    try:
        with open('sample_account_transactions.json', 'r', encoding='utf-8') as f:
            sample_data = json.load(f)
        
        with open('account_transactions.json', 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print("✅ تم نسخ البيانات التجريبية إلى الملف الرئيسي")
        print("🚀 يمكنك الآن تشغيل التطبيق ورؤية البيانات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نسخ البيانات: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("🎯 مولد البيانات التجريبية لسجل كشف الحساب الدوري")
    print("=" * 55)
    
    # إنشاء البيانات التجريبية
    if save_sample_data():
        print("\n" + "=" * 55)
        
        # سؤال المستخدم عن نسخ البيانات
        while True:
            choice = input("\n❓ هل تريد نسخ البيانات التجريبية إلى الملف الرئيسي؟ (y/n): ").lower().strip()
            
            if choice in ['y', 'yes', 'نعم', 'ن']:
                if copy_to_main_file():
                    break
                else:
                    break
            elif choice in ['n', 'no', 'لا', 'ل']:
                print("📝 يمكنك نسخ البيانات لاحقاً من ملف sample_account_transactions.json")
                break
            else:
                print("⚠️  يرجى الإجابة بـ y أو n")

if __name__ == "__main__":
    main()
