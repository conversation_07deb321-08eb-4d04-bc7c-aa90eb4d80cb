#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع لتطبيق سجل كشف الحساب الدوري
Quick launcher for Account Statement Application

هذا الملف يقوم بتشغيل التطبيق مع التحقق من المتطلبات
This file runs the application with requirements checking
"""

import sys
import os

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    # التحقق من tkinter
    try:
        import tkinter
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ خطأ: tkinter غير متوفر")
        print("يرجى تثبيت tkinter أو استخدام إصدار Python يحتوي عليه")
        return False
    
    # التحقق من المكتبات الاختيارية
    optional_libs = {
        'pandas': 'لتصدير البيانات إلى Excel',
        'openpyxl': 'للعمل مع ملفات Excel'
    }
    
    missing_optional = []
    for lib, purpose in optional_libs.items():
        try:
            __import__(lib)
            print(f"✅ {lib} متوفر")
        except ImportError:
            print(f"⚠️  {lib} غير متوفر ({purpose})")
            missing_optional.append(lib)
    
    if missing_optional:
        print("\n📝 ملاحظة: يمكنك تثبيت المكتبات الاختيارية باستخدام:")
        print("pip install pandas openpyxl")
        print("أو:")
        print("pip install -r requirements.txt")
    
    return True

def main():
    """تشغيل التطبيق الرئيسي"""
    print("🚀 بدء تشغيل تطبيق سجل كشف الحساب الدوري")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات الأساسية متوفرة")
    print("🎯 تشغيل التطبيق...")
    
    try:
        # استيراد وتشغيل التطبيق
        from account_statement import main as run_app
        run_app()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود ملف account_statement.py في نفس المجلد")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
