import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import json
import os
from typing import Dict, List, Tuple

class AccountStatementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("سجل كشف الحساب الدوري")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # تحديد الخط العربي
        self.arabic_font = ('Arial Unicode MS', 10)
        self.arabic_font_bold = ('Arial Unicode MS', 10, 'bold')
        self.arabic_font_large = ('Arial Unicode MS', 12, 'bold')
        
        # قاموس الحسابات
        self.accounts_dict = self.load_accounts()
        
        # قائمة العمليات
        self.transactions = []
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات المحفوظة
        self.load_data()
    
    def load_accounts(self) -> Dict[str, str]:
        """تحميل دليل الحسابات"""
        accounts = {
            # الموجودات
            "1": "الموجودات",
            "11": "الموجودات الثابتة",
            "111": "اراضي",
            "1111": "اراضي زراعية",
            "11111": "كلفة الشراء",
            "11112": "كلفة الاستصلاح",
            "1112": "اراضي بناء",
            "1113": "اراضي فضاء",
            "112": "أراضي بناء",
            "1121": "مباني",
            "1122": "مخازن وخزانات وسايلوات",
            "1123": "ابار و مناجم و مقالع",
            "11231": "ابار",
            "11232": "مناجم",
            "11233": "مقالع",
            "1125": "سدود و قنوات",
            "1126": "منشأت عائمة",
            "1127": "طرق و جسور",
            "1128": "منشأت نقل",
            "11281": "خطوط السكك الحديدية",
            "11282": "ارصفة الموانيء و المراسي",
            "11283": "مدارج المطارات",
            "1129": "منشأت اخرى",
            "11291": "مجاري المياة الثقيلة",
            "11292": "احواض مائية",
            "11293": "بيوت زجاجية",
            "11294": "خطوط انابيب الماء",
            "11295": "خطوط الكهرباء و الهاتف",
            "113": "الات و معدات",
            "114": "مباني ومنشات وطرق",
            "1141": "وسائل نقل بالسيارات",
            "11411": "وسائل نقل الركاب",
            "11412": "وسائل نقل البضائع",
            "11413": "عربات نقل اخرى",
            "1142": "وسائل نقل بالسكك الحديدية",
            "1143": "وسائل النقل النهري",
            "1144": "وسائل النقل البحري",
            "1145": "وسائل النقل الجوي",
            "1146": "خطوط انابيب النفط و الغاز",
            "115": "عدد وقوالب",
            "1151": "عدد",
            "1152": "قوالب",
            "1153": "خيم وجوادر",
            "116": "اثاث و اجهزة مكاتب",
            "1161": "اثاث",
            "1162": "اجهزة تكييف و تبريد",
            "1163": "حاسبات الكترونية",
            "1164": "الات حاسبة و كاتبة استنساخ",
            "1165": "ادوات و اجهزة مكاتب",
            "1166": "ستائر و مفروشات",
            "1167": "كتب و مراجع علمية",
            "117": "الموجودات الحية لاغراض الانتاج",
            "1171": "نباتات",
            "1172": "حيوانات",
            "118": "نفقات ايرادية مؤجلة",
            "1181": "نفقات التأسيس",
            "1182": "نفقات قبل التشغيل",
            "1183": "نفقات استكشاف و مسح",
            "1184": "نفقات ابحاث و تجارب",
            "1185": "موجودات ثابتة معنوية",
            "1186": "ديكورات و تركيبات وقواطع",
            "1187": "نفقات مؤجلة متنوعة",
            
            # مشروعات تحت التنفيذ
            "12": "مشروعات تحت التنفيذ",
            "121": "اراضي",
            "1211": "اراضي زراعية",
            "12111": "كلفة الشراء",
            "12112": "كلفة الاستصلاح",
            "1212": "اراضي بناء",
            "1213": "اراضي فضاء",
            "122": "مباني وانشأت وطرق",
            "1221": "مباني",
            "1222": "مخازن و خزانات و سايلوات",
            "1223": "منشأت سكنية",
            "1224": "ابار و مناجم و مقالع",
            "12241": "ابار",
            "12242": "مناجم",
            "12243": "مقالع",
            "1225": "سدود و قنوات",
            "1226": "منشأت عائمة",
            "1227": "طرق و جسور",
            "1228": "منشأت نقل",
            "12281": "خطوط السكك الحديدية",
            "12282": "ارصفة الموانيء و المراسي",
            "12283": "مدراج المطارات",
            "1229": "منشأت اخرى",
            "12291": "مجاري المياة الثقيلة",
            "12292": "احواض مائية",
            "12293": "بيوت زجاجية",
            "12294": "خطوط انابيب الماء",
            "12295": "خطوط الكهرباء والهاتف",
            "123": "الات و معدات",
            "124": "وسائل نقل وانتقال",
            "1241": "وسائل نقل بالسيارات",
            "12411": "وسائل نقل الركاب",
            "12412": "وسائل نقل البضائع",
            "12413": "عربات نقل اخرى",
            "1242": "وسائل نقل بالسكك الحديدية",
            "1243": "وسائل النقل النهري",
            "1244": "وسائل النقل البحري",
            "1245": "وسائل النقل الجوي",
            "1246": "خطوط انابيب النفط والغاز",
            "125": "عدد وقوالب",
            "1251": "عدد",
            "1252": "قوالب",
            "1253": "خيم وجوادر",
            "126": "اثاث واجهزة مكاتب",
            "1261": "اثاث",
            "1262": "اجهزة تكييف و تبريد",
            "1263": "حاسبات الكترونية",
            "1264": "الات حاسبة وكاتبة واستنساخ",
            "1265": "ادوات و اجهزة مكاتب",
            "1266": "ستائر ومفروشات",
            "1267": "كتب ومراجع علمية",
            "127": "نباتات وحيوانات",
            "1271": "نباتات",
            "1272": "حيوانات",
            "128": "نفقات ايرادية مؤجلة",
            "1281": "نفقات التأسيس",
            "1282": "نفقات قبل التشغيل",
            "1283": "نفقات استكشاف و مسح",
            "1284": "نفقات ابحاث وتجارب",
            "1285": "موجودات ثابتة معنوية",
            "1286": "ديكورات وتركيبات وقواطع",
            "1287": "نفقات مؤجلة متنوعة",
            "129": "انفاق استثماري",
            "1291": "دفعات مقدمة",
            "1292": "اعتمادات مستندية لموجودات ثابتة",
            
            # المخزون
            "13": "المخزون",
            "131": "مخزن الخامات والمواد الاولية",
            "132": "مخزن الوقود والزيوت",
            "1324": "فحم",
            "133": "مخزن الادوات الاحتياطية",
            "134": "مخزن مواد التعبئة والتغليف",
            "1341": "مخزن مواد التعبئة والتغليف المستهلكة",
            "1342": "مخزن مواد التعبئة المتداولة",
            "135": "مخصص خسائر متوقعة لمشاريع فيد الانجاز",
            "1351": "مخزون اللوازم والمهمات",
            "1352": "مخزن القرطاسية",
            "1353": "مخزن الكتب التعليمية",
            "1354": "مخزن المخلفات و المستهلكات",
            "13541": "مخزن المخلفات الأعتيادية",
            "13542": "اضرار الموجودات جراء العدوان",
            "13543": "اضرار الموجودات جراء صفحة الغدر والخيانة",
            "1355": "مخزن تجهيزات العاملين",
            "136": "مخزن الانتاج",
            "1361": "مخزون الانتاج التام",
            "1362": "مخزن الانتاج غير التام و الاعمال تحت التنفيذ",
            "1363": "أعمال تحت التنفيذ",
            "1364": "مخزون الانتاج غير التام النباتي",
            "1365": "مخزون الانتاج غير التام الحيواني",
            "137": "مخزون البضائع بغرض البيع",
            "138": "اعتمادات مستندية لشراء مواد",
            "1381": "اعتمادات مستندية لحساب االوحدة",
            "13811": "تامة الصنع",
            "13812": "خامات رئيسية",
            "13814": "ادوات احتياطية",
            "13815": "مواد اخرى",
            "1382": "بضاعة بطريق الشحن لحساب الوحدة",
            "1383": "اعتمادات مستندية لحساب الغير",
            "1384": "بضاعة بطريق الشحن لحساب الغير",
            "139": "مخزن المواد الاخرى",
            "1391": "بضائع لدى الغير",
            
            # القروض الممنوحة
            "14": "القروض الممنوحة",
            "141": "قروض ممنوحة طويلة الاجل",
            "1411": "قروض طويلة للقطاع الاشتراكي",
            "1412": "قروض طويلة للقطاع التعاوني",
            "1413": "قروض طويلة للقطاع المختلط",
            "1414": "قروض طويلة للقطاع الخاص",
            "1415": "قروض طويلة للعالم الخارجي",
            "142": "قروض ممنوحة قصيرة الاجل",
            "1421": "قروض قصيرة للقطاع الاشتراكي",
            "1422": "قروض قصيرة للقطاع التعاوني",
            "1423": "قروض قصيرة للقطاع المختلط",
            "1424": "قروض قصيرة للقطاع الخاص",
            "1425": "قروض قصيرة للعالم الخارجي",
            
            # الاستثمارات المالية
            "15": "الاستثمارات المالية",
            "151": "استثمارات طويلة الاجل",
            "1511": "استثمارات طويلة في القطاع الاشتراكي",
            "1512": "استثمارات طويلة في القطاع التعاوني",
            "1513": "استثمارات طويلة في القطاع المختلط",
            "1514": "استثمارات طويلة في القطاع الخاص",
            "1515": "استثمارات طويلة في العالم الخارجي",
            "152": "استثمارات قصيرة الاجل",
            "1521": "استثمارات قصيرة في القطاع الاشتراكي",
            "1522": "استثمارات قصيرة في القطاع التعاوني",
            "1523": "استثمارات قصيرة في القطاع المختلط",
            "1524": "استثمارات قصيرة في القطاع الخاص",
            "1525": "استثمارات قصيرة في القطاع الخارجي",
            
            # المدينون
            "16": "المدينون",
            "161": "عملاء",
            "1611": "عملاء قطاع اشتراكي",
            "1612": "عملاء قطاع تعاوني",
            "1613": "عملاء قطاع مختلط",
            "1614": "عملاء قطاع خاص",
            "1615": "عملاء عالم خارجي",
            "162": "اوراق القبض",
            "1621": "اوراق قبض قطاع اشتراكي",
            "1622": "اوراق قبض قطاع تعاوني",
            "1623": "اوراق قبض قطاع مختلط",
            "1624": "اوراق قبض قطاع خاص",
            "1625": "اوراق قبض عالم خارجي",
            "1626": "اوراق قبض برسم التحصيل",
            "163": "حسابات جارية مدنية",
            "1631": "حسابات جارية مدينة داخل المؤسسة",
            "1632": "حسابات جارية مدينة داخل المنشأة",
            "164": "سلف التعهدات الثانوية",
            "165": "مدينو النشاط غير الجاري",
            "166": "حسابات مدينة متنوعة",
            "1661": "تأمينات لدى الغير",
            "16611": "تأمينات لدى الغير طويلة الاجل",
            "16612": "تأمينات لدى الغير قصيرة الاجل",
            "1662": "ايرادات مستحقة",
            "1663": "مصاريف مدفوعة مقدما",
            "1664": "طلبات التعويض",
            "1665": "فروقات موجودات نقدية و مخزنية وثابتة مدنية",
            "16651": "فروقات نقدية لظروف طبيعية",
            "16652": "فروقات مخزنية لظروف طبيعية",
            "16653": "فروقات موجودات ثابتة لظروف طبيعية",
            "16654": "فروقات موجودات نقدية ومخزنية وثابتة لظروف غير طبيعية",
            "1666": "وزارة المالية - رواتب اجازات الأمومة",
            "1667": "وزارة المالية - رواتب الملتحقين بالخدمة العسكرية",
            "1668": "وزارة المالية - رواتب و مخصصات زوجات فاقدي البصر",
            "167": "سلف",
            "1671": "سلف لأغراض النشاط",
            "1672": "سلف المنتسبين",
            "1673": "سلف الزواج",
            
            # النقود
            "18": "النقود",
            "181": "نقدية بالصندوق",
            "1811": "صندوق المركز",
            "1812": "صندوق الفروع",
            "1813": "صندوق المعارض",
            "182": "سلف مستديمة",
            "183": "نقدية لدى المصارف",
            "184": "نقدية لدى الخزائن",
            "185": "شيكات و حوالات",
            "1851": "شيكات و حوالات قيد التحصيل",
            "1852": "حوالات بالطريق",
            "1853": "شيكات و حوالات مرفوضة"
        }
        
        # إضافة المطلوبات
        liabilities = {
            # المطلوبات
            "2": "المطلوبات",
            "21": "رأس المال",
            "211": "رأس المال المدفوع",
            "2111": "رأس المال المدفوع",
            "22": "الاحتياطات",
            "221": "احتياطات رأسمالية",
            "2211": "احتياطي التوسعات",
            "2212": "احتياطي توسعات مستخدم",
            "2213": "احتياطي الارباح الرأسمالية",
            "2214": "احتياطي استبدل الموجودات الثابتة",
            "222": "احتياطي عام",
            "223": "احتياطات متنوعة",
            "2231": "احتياطي البحث والتطوير",
            "2232": "احتياطي الخدمات الاجتماعية",
            "224": "الفائض المتراكم",
            "225": "العجز المتراكم (مدين)",
            "23": "التخصيصات",
            "231": "مخصص الاندثار المتراكم",
            "2312": "مخصص اندثار مباني و انشاءات و طرق",
            "2313": "مخصص اندثار الات و معدات",
            "2314": "مخصص اندثار وسائل نقل وانتقال",
            "2315": "مخصص اندثار عدد وقوالب",
            "2316": "مخصص اندثار اثاث و اجهزة مكاتب",
            "232": "مخصص الديون المشكوك في تحصيلها",
            "234": "مخصص مصروفات الشراء",
            "236": "مخصص مصاريف الصيانة المحتملة",
            "237": "مخصص هبوط اسعار المخزون السلعي",
            "238": "مخصص هبوط أسعار الاستثمارات المالية",
            "239": "تخصيصات متنوعة",
            "24": "القروض المستلمة",
            "241": "قروض مستلمة طويلة الاجل",
            "2411": "قروض طويلة من القطاع الاشتراكي",
            "2412": "قروض طويلة من القطاع التعاوني",
            "2413": "قروض طويلة من القطاع المختلط",
            "2414": "قروض طويلة من القطاع الخاص",
            "2415": "قروض طويلة من العالم الخارجي",
            "242": "قروض مستلمة قصيرة الاجل",
            "2421": "قروض قصيرة من القطاع الاشتراكي",
            "2422": "قروض قصيرة من القطاع التعاوني",
            "2423": "قروض قصيرة من القطاع المختلط",
            "2424": "قروض قصيرة من القطاع الخاص",
            "2425": "قروض قصيرة من العالم الخارجي",
            "25": "المصارف الدائنة",
            "251": "جاري مكشوف",
            "26": "الدائنون",
            "261": "مجهزون",
            "2611": "مجهزون قطاع اشتراكي",
            "2612": "مجهزون قطاع تعاوني",
            "2613": "مجهزون قطاع مختلط",
            "2614": "مجهزون قطاع خاص",
            "2615": "مجهزون عالم خارجي",
            "2616": "مجهزون بالدفع الاجل",
            "262": "اوراق دفع",
            "2621": "اوراق دفع قطاع اشتراكي",
            "2622": "اوراق دفع قطاع تعاوني",
            "2623": "اوراق دفع قطاع مختلط",
            "2624": "اوراق دفع قطاع خاص",
            "2625": "اوراق دفع عالم خارجي",
            "263": "حسابات جارية دائنة",
            "2631": "حسابات جارية دائنة داخل المؤسسة",
            "2632": "حسابات جارية دائنة داخل المنشأة",
            "264": "حسابات التعهدات",
            "2641": "سلف مستلمة مقدما",
            "2642": "الذرعات المنجزة",
            "265": "دائنو نشاط غير جاري",
            "266": "حسابات دائنة متنوعة",
            "2661": "تأمينات مستلمة وحسابات التوفير",
            "26611": "تأمينات مستلمة طويلة الاجل",
            "26612": "تأمينات مستلمة قصيرة الاجل",
            "26613": "حسابات التوفير",
            "2662": "ايرادات مستلمة مقدما",
            "2663": "مصاريف مستحقة",
            "2664": "رواتب و اجور مستحقة",
            "2665": "رواتب و اجور معادة",
            "2666": "مديرية التقاعد العامة",
            "2667": "مؤسسة التقاعد و الضمان الاجتماعي للعمال",
            "2668": "فروقات نقدية و مخزنية دائنة",
            "26681": "فروقات نقدية",
            "26682": "فروقات مخزنية",
            "2669": "تعويضات وغرامات مؤجلة",
            "267": "استقطاعات لحساب الغير",
            "2671": "استقطاعات من المنتسبين لحساب الغير",
            "2672": "استقطاعات من غبر المنتسبين لحساب الغير",
            "268": "دائنو توزيع الارباح",
            "2681": "حصة الخزينة العامة",
            "2682": "حصة العاملين",
            "28": "حساب العمليات الجارية",
            "281": "حساب النشاط الجاري"
        }

        # إضافة الاستخدامات
        expenses = {
            # الاستخدامات
            "3": "الاستخدامات",
            "31": "رواتب و اجور",
            "311": "الرواتب النقدية للموظفين",
            "3111": "رواتب",
            "3112": "مخصصات عائلية",
            "3113": "اجور اعمال اضافية",
            "3114": "مكافأت تشجيعية",
            "3115": "مخصصات مهنية و فنية",
            "3116": "مخصصات تعويضية",
            "3117": "م. غلاء معيشة",
            "3118": "م. مقطوعة",
            "3119": "مخصصات اخرى",
            "312": "الاجور النقدية للعمال",
            "3121": "اجور",
            "3122": "مخصصات عائلية",
            "3123": "اجور اعمال اضافية",
            "3124": "مكافأت تشجيعية",
            "3125": "مخصصات مهنية و فنية",
            "3126": "مخصصات تعويضية",
            "3129": "مخصصات اخرى",
            "313": "رواتب و اجور و مخصصات غير العراقيين",
            "3133": "اجور اعمال اضافية",
            "314": "المساهمة في الضمان الاجتماعي للموظفين",
            "3141": "حصة المنشأة في التقاعد",
            "3142": "التأمين على الموظفين",
            "3143": "الضمان الصحي للموظفين",
            "315": "المساهمة في الضمان الاجتماعي للعمال",
            "3151": "حصة المنشأة في الضمان",
            "3152": "التأمين على العمال",
            "3153": "الضمان الصحي للعمال",
            "316": "المساهمة في الضمان الاجتماعي لغير العراقيين",
            "32": "المستلزمات السلعية",
            "321": "الخامات و المواد الاولية",
            "322": "الوقود والزيوت",
            "3221": "مواد نفطية",
            "3222": "غاز",
            "3223": "زيوت وشحوم",
            "323": "الادوات الاحتياطية",
            "324": "مواد التعبئة و التغليف",
            "3241": "مواد التعبئة و التغليف المستهلكة",
            "3242": "مواد التعبئة المتداولة",
            "325": "المتنوعات",
            "3251": "اللوازم و المهمات",
            "3252": "القرطاسية",
            "32521": "قرطاسية",
            "32522": "برامجيات الحاسوب",
            "3253": "الكتب التعليمية",
            "3254": "المخلفات و المستهلكات",
            "326": "تجهيزات العاملين",
            "3261": "كساوي",
            "3262": "مواد غذائية",
            "3263": "مواد طبية",
            "327": "المياة و الكهرباء",
            "3271": "المياة",
            "3272": "الكهرباء"
        }

        # إضافة الموارد
        revenues = {
            # الموارد
            "4": "الموارد",
            "41": "ايراد نشاط الانتاج السلعي",
            "411": "ايراد نشاط الصناعات الاستخراجية",
            "4111": "صافي المبيعات",
            "4112": "تغير مخزون انتاج تام",
            "4113": "تغير مخزون انتاج غير تام",
            "412": "ايراد نشاط الصناعات التحويلية",
            "4121": "صافي المبيعات",
            "41211": "مبيعات الواح",
            "4123": "تغير مخزون انتاج غير تام",
            "413": "ايراد نشاط التشييد",
            "4131": "ايرادات ذرعات منجزة",
            "4132": "تغير اعمال تحت التنفيذ",
            "414": "ايراد نشاط الانتاج النباتي",
            "4141": "صافي المبيعات",
            "4142": "تغير مخزون انتاج تام",
            "4143": "زراعة قائمة",
            "415": "ايراد نشاط الانتاج الحيواني",
            "4151": "صافي المبيعات",
            "4152": "تغير مخزون انتاج تام",
            "4153": "تغير مخزون انتاج غير تام",
            "416": "ايراد ماء و كهرباء",
            "4161": "ايراد ماء",
            "4162": "ايراد كهرباء",
            "417": "ايراد بيع مخلفات",
            "42": "ايراد النشاط التجاري",
            "421": "صافي مبيعات بضائع بغرض البيع",
            "422": "تغير مخزون بضائع بغرض البيع",
            "423": "عمولة مستلمة",
            "424": "ايراد الفندقة و السياحة",
            "425": "ايرادات متنوعة",
            "43": "ايراد النشاط الخدمي",
            "431": "ايراد خدمات النقل",
            "432": "ايراد خدمات الاتصالات",
            "433": "ايراد خدمات الصيانة و التصليح",
            "436": "ايراد انتساب و اشتراك",
            "437": "ايراد خدمات متنوعة",
            "441": "ايراد التشغيل للغير",
            "45": "كلفة الموجودات المصنعة داخليا",
            "451": "كلفة الموجودات الثابتة المصنعة",
            "452": "كلفة الادوات الاحتياطية المصنعة",
            "453": "كلفة مواد التعبئة المتداولة المصنعة",
            "46": "الفوائد الدائنة وايجارات الأراضي",
            "461": "فوائد دائنة",
            "462": "ايجارات الاراضي",
            "463": "ايرادات الاستثمارات المالية",
            "47": "الاعانات",
            "471": "اعانات سلع مستوردة",
            "472": "اعانات انتاج محلي",
            "473": "اعانات تصدير",
            "474": "اعانات اخرى",
            "48": "الايرادات التحويلية",
            "481": "ايرادات التقاعد و الضمان الاجتماعي",
            "4811": "مساهمة التقاعد والضمان الاجتماعي",
            "4812": "حصة الضمان من ارباح المنشأت",
            "482": "منح تحويلية",
            "4821": "منحة الخزينة العامة",
            "4822": "منح الوحدات المركزية او التابعة",
            "483": "ايرادات تحويلية متنوعة",
            "4831": "تبرعات مستلمة",
            "4832": "تعويضات و غرامات",
            "4833": "ديون سبق شطبها",
            "49": "الايرادات الاخرى",
            "491": "ايرادات سنوات سابقة",
            "492": "ايرادات عرضية"
        }

        accounts.update(liabilities)
        accounts.update(expenses)
        accounts.update(revenues)

        return accounts

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="سجل كشف الحساب الدوري",
                              font=self.arabic_font_large, fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        # إطار الإدخال
        input_frame = tk.LabelFrame(self.root, text="إدخال العملية",
                                   font=self.arabic_font_bold, bg='#ecf0f1',
                                   relief='groove', bd=2)
        input_frame.pack(fill='x', padx=10, pady=5)

        # الصف الأول - التاريخ ورقم المستند
        row1_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row1_frame.pack(fill='x', padx=10, pady=5)

        # التاريخ
        tk.Label(row1_frame, text="التاريخ:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.date_entry = tk.Entry(row1_frame, font=self.arabic_font, width=12)
        self.date_entry.pack(side='right', padx=5)
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # رقم المستند
        tk.Label(row1_frame, text="رقم المستند:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.doc_number_entry = tk.Entry(row1_frame, font=self.arabic_font, width=15)
        self.doc_number_entry.pack(side='right', padx=5)

        # الصف الثاني - كود الحساب واسم الحساب
        row2_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row2_frame.pack(fill='x', padx=10, pady=5)

        # كود الحساب
        tk.Label(row2_frame, text="كود الحساب:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.account_code_entry = tk.Entry(row2_frame, font=self.arabic_font, width=15)
        self.account_code_entry.pack(side='right', padx=5)
        self.account_code_entry.bind('<KeyRelease>', self.on_account_code_change)

        # اسم الحساب (للعرض فقط)
        tk.Label(row2_frame, text="اسم الحساب:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.account_name_var = tk.StringVar()
        self.account_name_label = tk.Label(row2_frame, textvariable=self.account_name_var,
                                          font=self.arabic_font, bg='#ecf0f1', fg='#27ae60', width=30)
        self.account_name_label.pack(side='right', padx=5)

        # الصف الثالث - البيان والمبلغ
        row3_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row3_frame.pack(fill='x', padx=10, pady=5)

        # البيان
        tk.Label(row3_frame, text="البيان:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.description_entry = tk.Entry(row3_frame, font=self.arabic_font, width=30)
        self.description_entry.pack(side='right', padx=5)

        # المبلغ
        tk.Label(row3_frame, text="المبلغ:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.amount_entry = tk.Entry(row3_frame, font=self.arabic_font, width=15)
        self.amount_entry.pack(side='right', padx=5)

        # الصف الرابع - نوع العملية والأزرار
        row4_frame = tk.Frame(input_frame, bg='#ecf0f1')
        row4_frame.pack(fill='x', padx=10, pady=5)

        # نوع العملية
        tk.Label(row4_frame, text="نوع العملية:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.transaction_type = tk.StringVar(value="مدين")
        debit_radio = tk.Radiobutton(row4_frame, text="مدين", variable=self.transaction_type,
                                    value="مدين", font=self.arabic_font, bg='#ecf0f1')
        debit_radio.pack(side='right', padx=5)
        credit_radio = tk.Radiobutton(row4_frame, text="دائن", variable=self.transaction_type,
                                     value="دائن", font=self.arabic_font, bg='#ecf0f1')
        credit_radio.pack(side='right', padx=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(row4_frame, bg='#ecf0f1')
        buttons_frame.pack(side='left', padx=10)

        add_btn = tk.Button(buttons_frame, text="إضافة", font=self.arabic_font_bold,
                           bg='#27ae60', fg='white', width=8, command=self.add_transaction)
        add_btn.pack(side='left', padx=2)

        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.arabic_font_bold,
                             bg='#e74c3c', fg='white', width=8, command=self.clear_form)
        clear_btn.pack(side='left', padx=2)

        # إطار عرض العمليات
        display_frame = tk.LabelFrame(self.root, text="كشف الحساب",
                                     font=self.arabic_font_bold, bg='#ecf0f1',
                                     relief='groove', bd=2)
        display_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إطار البحث والفلترة
        search_frame = tk.Frame(display_frame, bg='#ecf0f1')
        search_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(search_frame, text="البحث بكود الحساب:", font=self.arabic_font, bg='#ecf0f1').pack(side='right', padx=5)
        self.search_entry = tk.Entry(search_frame, font=self.arabic_font, width=15)
        self.search_entry.pack(side='right', padx=5)
        self.search_entry.bind('<KeyRelease>', self.filter_transactions)

        search_btn = tk.Button(search_frame, text="بحث", font=self.arabic_font_bold,
                              bg='#3498db', fg='white', width=8, command=self.filter_transactions)
        search_btn.pack(side='right', padx=5)

        show_all_btn = tk.Button(search_frame, text="عرض الكل", font=self.arabic_font_bold,
                                bg='#9b59b6', fg='white', width=8, command=self.show_all_transactions)
        show_all_btn.pack(side='right', padx=5)

        # جدول العمليات
        columns = ('التاريخ', 'رقم المستند', 'كود الحساب', 'اسم الحساب', 'البيان', 'مدين', 'دائن', 'الرصيد')
        self.tree = ttk.Treeview(display_frame, columns=columns, show='headings', height=15)

        # تحديد عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            if col in ['مدين', 'دائن', 'الرصيد']:
                self.tree.column(col, width=100, anchor='center')
            elif col == 'كود الحساب':
                self.tree.column(col, width=80, anchor='center')
            elif col == 'اسم الحساب':
                self.tree.column(col, width=200, anchor='e')
            else:
                self.tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(display_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)

        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.root, text="الإحصائيات",
                                   font=self.arabic_font_bold, bg='#ecf0f1',
                                   relief='groove', bd=2)
        stats_frame.pack(fill='x', padx=10, pady=5)

        stats_inner_frame = tk.Frame(stats_frame, bg='#ecf0f1')
        stats_inner_frame.pack(fill='x', padx=10, pady=5)

        # إجمالي المدين
        tk.Label(stats_inner_frame, text="إجمالي المدين:", font=self.arabic_font_bold, bg='#ecf0f1').pack(side='right', padx=10)
        self.total_debit_var = tk.StringVar(value="0.00")
        tk.Label(stats_inner_frame, textvariable=self.total_debit_var, font=self.arabic_font,
                bg='#ecf0f1', fg='#e74c3c').pack(side='right', padx=5)

        # إجمالي الدائن
        tk.Label(stats_inner_frame, text="إجمالي الدائن:", font=self.arabic_font_bold, bg='#ecf0f1').pack(side='right', padx=10)
        self.total_credit_var = tk.StringVar(value="0.00")
        tk.Label(stats_inner_frame, textvariable=self.total_credit_var, font=self.arabic_font,
                bg='#ecf0f1', fg='#27ae60').pack(side='right', padx=5)

        # الرصيد النهائي
        tk.Label(stats_inner_frame, text="الرصيد النهائي:", font=self.arabic_font_bold, bg='#ecf0f1').pack(side='right', padx=10)
        self.final_balance_var = tk.StringVar(value="0.00")
        tk.Label(stats_inner_frame, textvariable=self.final_balance_var, font=self.arabic_font_bold,
                bg='#ecf0f1', fg='#2c3e50').pack(side='right', padx=5)

        # أزرار إضافية
        extra_buttons_frame = tk.Frame(stats_frame, bg='#ecf0f1')
        extra_buttons_frame.pack(fill='x', padx=10, pady=5)

        save_btn = tk.Button(extra_buttons_frame, text="حفظ البيانات", font=self.arabic_font_bold,
                            bg='#16a085', fg='white', width=12, command=self.save_data)
        save_btn.pack(side='left', padx=5)

        load_btn = tk.Button(extra_buttons_frame, text="تحميل البيانات", font=self.arabic_font_bold,
                            bg='#f39c12', fg='white', width=12, command=self.load_data)
        load_btn.pack(side='left', padx=5)

        export_btn = tk.Button(extra_buttons_frame, text="تصدير إلى Excel", font=self.arabic_font_bold,
                              bg='#8e44ad', fg='white', width=12, command=self.export_to_excel)
        export_btn.pack(side='left', padx=5)

        delete_btn = tk.Button(extra_buttons_frame, text="حذف العملية", font=self.arabic_font_bold,
                              bg='#c0392b', fg='white', width=12, command=self.delete_transaction)
        delete_btn.pack(side='right', padx=5)

    def on_account_code_change(self, event):
        """تحديث اسم الحساب عند تغيير كود الحساب"""
        account_code = self.account_code_entry.get().strip()
        if account_code in self.accounts_dict:
            self.account_name_var.set(self.accounts_dict[account_code])
        else:
            self.account_name_var.set("حساب غير موجود")

    def add_transaction(self):
        """إضافة عملية جديدة"""
        try:
            # التحقق من صحة البيانات
            date_str = self.date_entry.get().strip()
            doc_number = self.doc_number_entry.get().strip()
            account_code = self.account_code_entry.get().strip()
            description = self.description_entry.get().strip()
            amount_str = self.amount_entry.get().strip()
            transaction_type = self.transaction_type.get()

            if not all([date_str, doc_number, account_code, description, amount_str]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if account_code not in self.accounts_dict:
                messagebox.showerror("خطأ", "كود الحساب غير موجود في دليل الحسابات")
                return

            try:
                amount = float(amount_str)
                if amount <= 0:
                    messagebox.showerror("خطأ", "المبلغ يجب أن يكون أكبر من الصفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "المبلغ يجب أن يكون رقماً صحيحاً")
                return

            # إنشاء العملية
            transaction = {
                'date': date_str,
                'doc_number': doc_number,
                'account_code': account_code,
                'account_name': self.accounts_dict[account_code],
                'description': description,
                'amount': amount,
                'type': transaction_type,
                'timestamp': datetime.now().isoformat()
            }

            self.transactions.append(transaction)
            self.refresh_display()
            self.clear_form()
            messagebox.showinfo("نجح", "تم إضافة العملية بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة العملية: {str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.date_entry.delete(0, tk.END)
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        self.doc_number_entry.delete(0, tk.END)
        self.account_code_entry.delete(0, tk.END)
        self.description_entry.delete(0, tk.END)
        self.amount_entry.delete(0, tk.END)
        self.account_name_var.set("")
        self.transaction_type.set("مدين")

    def refresh_display(self, filtered_transactions=None):
        """تحديث عرض العمليات"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        # تحديد العمليات المراد عرضها
        transactions_to_show = filtered_transactions if filtered_transactions is not None else self.transactions

        # ترتيب العمليات حسب التاريخ
        sorted_transactions = sorted(transactions_to_show, key=lambda x: x['date'])

        # حساب الأرصدة
        balance = 0
        total_debit = 0
        total_credit = 0

        for transaction in sorted_transactions:
            if transaction['type'] == 'مدين':
                debit_amount = f"{transaction['amount']:,.2f}"
                credit_amount = ""
                balance += transaction['amount']
                total_debit += transaction['amount']
            else:
                debit_amount = ""
                credit_amount = f"{transaction['amount']:,.2f}"
                balance -= transaction['amount']
                total_credit += transaction['amount']

            # إدراج العملية في الجدول
            self.tree.insert('', 'end', values=(
                transaction['date'],
                transaction['doc_number'],
                transaction['account_code'],
                transaction['account_name'],
                transaction['description'],
                debit_amount,
                credit_amount,
                f"{balance:,.2f}"
            ))

        # تحديث الإحصائيات
        self.total_debit_var.set(f"{total_debit:,.2f}")
        self.total_credit_var.set(f"{total_credit:,.2f}")
        self.final_balance_var.set(f"{balance:,.2f}")

    def filter_transactions(self, event=None):
        """فلترة العمليات حسب كود الحساب"""
        search_code = self.search_entry.get().strip()
        if not search_code:
            self.show_all_transactions()
            return

        filtered = [t for t in self.transactions if search_code in t['account_code']]
        self.refresh_display(filtered)

    def show_all_transactions(self):
        """عرض جميع العمليات"""
        self.search_entry.delete(0, tk.END)
        self.refresh_display()

    def delete_transaction(self):
        """حذف العملية المحددة"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى تحديد عملية للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه العملية؟"):
            # الحصول على بيانات العملية المحددة
            item_values = self.tree.item(selected_item[0])['values']

            # البحث عن العملية في القائمة وحذفها
            for i, transaction in enumerate(self.transactions):
                if (transaction['date'] == item_values[0] and
                    transaction['doc_number'] == item_values[1] and
                    transaction['account_code'] == item_values[2]):
                    del self.transactions[i]
                    break

            self.refresh_display()
            messagebox.showinfo("نجح", "تم حذف العملية بنجاح")

    def save_data(self):
        """حفظ البيانات في ملف JSON"""
        try:
            with open('account_transactions.json', 'w', encoding='utf-8') as f:
                json.dump(self.transactions, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("نجح", "تم حفظ البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def load_data(self):
        """تحميل البيانات من ملف JSON"""
        try:
            if os.path.exists('account_transactions.json'):
                with open('account_transactions.json', 'r', encoding='utf-8') as f:
                    self.transactions = json.load(f)
                self.refresh_display()
                messagebox.showinfo("نجح", "تم تحميل البيانات بنجاح")
            else:
                messagebox.showinfo("معلومات", "لا يوجد ملف بيانات محفوظ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def export_to_excel(self):
        """تصدير البيانات إلى ملف Excel"""
        try:
            import pandas as pd

            if not self.transactions:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # تحضير البيانات للتصدير
            data = []
            balance = 0

            for transaction in sorted(self.transactions, key=lambda x: x['date']):
                if transaction['type'] == 'مدين':
                    debit_amount = transaction['amount']
                    credit_amount = 0
                    balance += transaction['amount']
                else:
                    debit_amount = 0
                    credit_amount = transaction['amount']
                    balance -= transaction['amount']

                data.append({
                    'التاريخ': transaction['date'],
                    'رقم المستند': transaction['doc_number'],
                    'كود الحساب': transaction['account_code'],
                    'اسم الحساب': transaction['account_name'],
                    'البيان': transaction['description'],
                    'مدين': debit_amount,
                    'دائن': credit_amount,
                    'الرصيد': balance
                })

            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(data)
            filename = f"account_statement_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            df.to_excel(filename, index=False, engine='openpyxl')

            messagebox.showinfo("نجح", f"تم تصدير البيانات إلى ملف: {filename}")

        except ImportError:
            messagebox.showerror("خطأ", "يرجى تثبيت مكتبة pandas و openpyxl لتصدير البيانات:\npip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = AccountStatementApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
