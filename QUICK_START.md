# دليل البدء السريع - سجل كشف الحساب الدوري

## 🚀 التشغيل السريع

### الطريقة الأولى (Windows):
```
انقر مرتين على ملف: run_app.bat
```

### الطريقة الثانية (جميع الأنظمة):
```bash
python account_statement.py
```

### الطريقة الثالثة (مع فحص المتطلبات):
```bash
python run_app.py
```

## 📝 كيفية الاستخدام

### 1. إضافة عملية جديدة:
1. **التاريخ**: سيظهر التاريخ الحالي تلقائياً
2. **رقم المستند**: أدخل رقم المستند (مثل: DOC-001)
3. **كود الحساب**: أدخل كود الحساب (مثل: 1811)
   - ✨ **سيظهر اسم الحساب تلقائياً!**
4. **البيان**: وصف العملية (مثل: شراء مواد خام)
5. **المبلغ**: قيمة العملية (مثل: 1000)
6. **نوع العملية**: اختر مدين أو دائن
7. اضغط **إضافة**

### 2. أكواد الحسابات الشائعة:
| الكود | اسم الحساب |
|-------|-------------|
| 1811 | صندوق المركز |
| 183 | نقدية لدى المصارف |
| 161 | عملاء |
| 261 | مجهزون |
| 321 | الخامات والمواد الأولية |
| 3111 | رواتب |
| 4121 | صافي المبيعات |
| 322 | الوقود والزيوت |
| 1161 | أثاث |
| 2111 | رأس المال المدفوع |

### 3. البحث والفلترة:
- **البحث بكود الحساب**: أدخل كود الحساب في حقل البحث
- **عرض الكل**: اضغط زر "عرض الكل" لإظهار جميع العمليات

### 4. إدارة البيانات:
- **حفظ البيانات**: حفظ العمليات في ملف JSON
- **تحميل البيانات**: استرداد العمليات المحفوظة
- **تصدير إلى Excel**: إنشاء ملف Excel
- **حذف العملية**: حدد عملية واضغط "حذف العملية"

## 💡 نصائح مهمة

### ✅ أمثلة على العمليات الصحيحة:

**عملية شراء نقدي:**
- كود الحساب: 321 (الخامات والمواد الأولية) - مدين
- كود الحساب: 1811 (صندوق المركز) - دائن

**عملية بيع نقدي:**
- كود الحساب: 1811 (صندوق المركز) - مدين  
- كود الحساب: 4121 (صافي المبيعات) - دائن

**عملية دفع راتب:**
- كود الحساب: 3111 (رواتب) - مدين
- كود الحساب: 1811 (صندوق المركز) - دائن

### 🔍 البحث السريع:
- اكتب جزء من كود الحساب للبحث
- مثال: اكتب "18" للعثور على جميع حسابات النقدية

### 📊 قراءة الإحصائيات:
- **إجمالي المدين**: مجموع جميع العمليات المدينة
- **إجمالي الدائن**: مجموع جميع العمليات الدائنة  
- **الرصيد النهائي**: الفرق بين المدين والدائن

## 🛠️ حل المشاكل الشائعة

### المشكلة: "حساب غير موجود"
**الحل**: تأكد من كتابة كود الحساب بشكل صحيح

### المشكلة: لا يمكن تصدير Excel
**الحل**: ثبت المكتبات المطلوبة:
```bash
pip install pandas openpyxl
```

### المشكلة: الخط لا يظهر العربية بشكل صحيح
**الحل**: تأكد من وجود خط "Arial Unicode MS" على النظام

## 📁 الملفات المهمة

- `account_statement.py` - التطبيق الرئيسي
- `account_transactions.json` - ملف حفظ البيانات
- `run_app.py` - مشغل التطبيق مع فحص المتطلبات
- `run_app.bat` - مشغل Windows السريع

## 🎯 بيانات تجريبية

التطبيق يحتوي على بيانات تجريبية جاهزة لتجربة جميع الميزات!

---

**استمتع باستخدام سجل كشف الحساب الدوري! 🎉**
